<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassroomResource\Pages;

use App\Filament\Resources\ClassroomResource;
use App\Models\Classroom;
use App\Models\Student;
use BackedEnum;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;

class ManageStudents extends ManageRelatedRecords
{
    protected static string $resource = ClassroomResource::class;

    protected static string $relationship = 'students';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-user-group';

    public static function getNavigationLabel(): string
    {
        return __('Students');
    }

    public function getTitle(): string
    {
        return __('Students');
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->disabled()
            ->columns(1)
            ->components([
                Section::make(__('Student Information'))
                    ->schema([
                        TextInput::make('name')
                            ->label('Full Name')
                            ->translateLabel()
                            ->required()
                            ->maxLength(255)
                            ->placeholder(__('Enter student full name')),

                        TextInput::make('age')
                            ->label('Age')
                            ->translateLabel()
                            ->numeric()
                            ->required()
                            ->minValue(3)
                            ->maxValue(25)
                            ->placeholder('Age'),

                        DatePicker::make('date_of_birth')
                            ->label('Date of Birth')
                            ->translateLabel()
                            ->required()
                            ->maxDate(now())
                            ->displayFormat('Y-m-d'),

                        Select::make('gender')
                            ->label('Gender')
                            ->translateLabel()
                            ->options([
                                'male' => __('Male'),
                                'female' => __('Female'),
                            ])
                            ->required(),

                        TextInput::make('grade_level')
                            ->label('Grade Level')
                            ->translateLabel()
                            ->maxLength(255)
                            ->placeholder(__('e.g., Grade 5, High School')),

                        Toggle::make('is_active')
                            ->label('Active Student')
                            ->translateLabel()
                            ->default(true),
                    ])->columns(2),

                Section::make(__('Additional Information'))
                    ->schema([
                        Textarea::make('medical_conditions')
                            ->label('Medical Conditions')
                            ->translateLabel()
                            ->rows(3)
                            ->placeholder(__('Any medical conditions or allergies')),

                        Textarea::make('notes')
                            ->label('Notes')
                            ->translateLabel()
                            ->rows(3)
                            ->placeholder(__('Additional notes about the student')),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name')
                    ->label('Full Name')
                    ->translateLabel()
                    ->searchable()
                    ->sortable(),

                TextColumn::make('age')
                    ->label('Age')
                    ->translateLabel()
                    ->sortable(),

                TextColumn::make('date_of_birth')
                    ->label('Date of Birth')
                    ->translateLabel()
                    ->date()
                    ->sortable(),

                TextColumn::make('gender')
                    ->label('Gender')
                    ->translateLabel()
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'male' => __('Male'),
                        'female' => __('Female'),
                        default => $state,
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'male' => 'info',
                        'female' => 'warning',
                        default => 'gray',
                    }),

                TextColumn::make('grade_level')
                    ->label('Grade Level')
                    ->translateLabel()
                    ->searchable(),

                IconColumn::make('is_active')
                    ->label('Active Status')
                    ->translateLabel()
                    ->boolean()
                    ->label('Active')
                    ->translateLabel(),

                TextColumn::make('created_at')
                    ->label('Created At')
                    ->translateLabel()
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('gender')
                    ->label('Gender')
                    ->translateLabel()
                    ->options([
                        'male' => __('Male'),
                        'female' => __('Female'),
                    ]),

                TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->translateLabel(),
            ])
            ->headerActions([
                Action::make('AddStudent')
                    ->label('Add Student')
                    ->translateLabel()
                    ->modalWidth('xl')
                    ->schema([
                        Select::make('student_id')
                            ->label('Student')
                            ->translateLabel()
                            ->options(
                                Student::where(function ($query) {
                                    $query->whereNull('classroom_id')
                                        ->orWhere('classroom_id', '');
                                })->pluck('name', 'id'))
                            ->preload()
                            ->searchable()
                            ->required(),
                    ])
                    ->action(function (array $data): void {

                        // chack classroom capacity before adding student
                        if ($this->record->students()->count() >= $this->record->capacity) {
                            Notification::make()
                                ->title(__('Classroom is full'))
                                ->body(__('The classroom is full. Please remove a student before adding a new one.'))
                                ->danger()
                                ->send();

                            return;
                        }

                        $student = Student::find($data['student_id']);

                        /** @var Classroom $classroom */
                        $classroom = $this->record;
                        if ($student) {
                            $student->classroom_id = $classroom->id;
                            $student->save();
                        }

                        Notification::make()
                            ->title(__('Student Added'))
                            ->body(__('The student has been added to the classroom successfully.'))
                            ->success()
                            ->send();
                    })
                    ->modalHeading(__('Add Student'))
                    ->modalSubmitActionLabel(__('Add Student')),
            ])
            ->recordActions([
                ViewAction::make()->slideOver(),
                Action::make('removeStudent')
                    ->label('Remove Student')
                    ->translateLabel()
                    ->color('danger')
                    ->requiresConfirmation()
                    ->action(function (Student $record): void {
                        $record->classroom_id = null;
                        $record->save();
                    }),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
